﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Content;

namespace CoreHub
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            try
            {
                base.OnCreate(savedInstanceState);

                // 检查是否是从更新通知点击进入的
                CheckUpdateNotificationIntent();
            }
            catch (System.Exception ex)
            {
                // 记录启动错误
                System.Diagnostics.Debug.WriteLine($"MainActivity OnCreate Error: {ex}");

                // 可以在这里添加更详细的错误处理
                // 比如显示错误对话框或重启应用
                throw;
            }
        }

        protected override void OnNewIntent(Intent? intent)
        {
            base.OnNewIntent(intent);
            Intent = intent;
            CheckUpdateNotificationIntent();
        }

        private void CheckUpdateNotificationIntent()
        {
            try
            {
                if (Intent?.GetBooleanExtra("show_update", false) == true)
                {
                    // 延迟一下，等待应用完全加载
                    Task.Delay(1000).ContinueWith(_ =>
                    {
                        // 导航到首页并触发更新检查
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            // 这里可以通过消息传递或事件通知Blazor页面显示更新对话框
                            System.Diagnostics.Debug.WriteLine("从更新通知启动，应该显示更新对话框");
                        });
                    });
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理更新通知Intent时发生错误: {ex}");
            }
        }
    }
}
