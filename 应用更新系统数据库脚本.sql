-- =============================================
-- 应用更新系统数据库脚本
-- 创建时间: 2024-12-19
-- 描述: 创建应用版本管理相关表结构
-- =============================================

USE [CoreHub]
GO

-- 创建应用版本表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AppVersions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AppVersions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [VersionNumber] [nvarchar](20) NOT NULL,
        [VersionCode] [int] NOT NULL,
        [Platform] [nvarchar](20) NOT NULL,
        [Title] [nvarchar](100) NOT NULL,
        [Description] [ntext] NULL,
        [UpdateType] [nvarchar](20) NOT NULL DEFAULT 'Minor',
        [IsForceUpdate] [bit] NOT NULL DEFAULT 0,
        [DownloadUrl] [nvarchar](500) NOT NULL,
        [FileSize] [bigint] NOT NULL DEFAULT 0,
        [FileMd5] [nvarchar](32) NULL,
        [MinSupportedVersionCode] [int] NULL,
        [Status] [nvarchar](20) NOT NULL DEFAULT 'Draft',
        [TargetAudience] [nvarchar](20) NOT NULL DEFAULT 'All',
        [TargetDepartmentIds] [ntext] NULL,
        [ReleaseTime] [datetime] NULL,
        [CreatedAt] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] [int] NOT NULL,
        [UpdatedAt] [datetime] NULL,
        [UpdatedBy] [int] NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT 1,
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_AppVersions] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    PRINT '✓ 创建 AppVersions 表成功'
END
ELSE
BEGIN
    PRINT '⚠ AppVersions 表已存在，跳过创建'
END
GO

-- 创建索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AppVersions_Platform_VersionCode')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AppVersions_Platform_VersionCode] ON [dbo].[AppVersions]
    (
        [Platform] ASC,
        [VersionCode] DESC
    )
    PRINT '✓ 创建 IX_AppVersions_Platform_VersionCode 索引成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AppVersions_Status_Platform')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AppVersions_Status_Platform] ON [dbo].[AppVersions]
    (
        [Status] ASC,
        [Platform] ASC,
        [IsEnabled] ASC
    )
    PRINT '✓ 创建 IX_AppVersions_Status_Platform 索引成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AppVersions_TargetAudience')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_AppVersions_TargetAudience] ON [dbo].[AppVersions]
    (
        [TargetAudience] ASC,
        [Platform] ASC,
        [Status] ASC
    )
    PRINT '✓ 创建 IX_AppVersions_TargetAudience 索引成功'
END
GO

-- 添加外键约束（如果Users表存在）
IF EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AppVersions_CreatedBy_Users')
    BEGIN
        ALTER TABLE [dbo].[AppVersions] ADD CONSTRAINT [FK_AppVersions_CreatedBy_Users] 
        FOREIGN KEY([CreatedBy]) REFERENCES [dbo].[Users] ([Id])
        PRINT '✓ 创建 FK_AppVersions_CreatedBy_Users 外键约束成功'
    END

    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AppVersions_UpdatedBy_Users')
    BEGIN
        ALTER TABLE [dbo].[AppVersions] ADD CONSTRAINT [FK_AppVersions_UpdatedBy_Users] 
        FOREIGN KEY([UpdatedBy]) REFERENCES [dbo].[Users] ([Id])
        PRINT '✓ 创建 FK_AppVersions_UpdatedBy_Users 外键约束成功'
    END
END
ELSE
BEGIN
    PRINT '⚠ Users 表不存在，跳过外键约束创建'
END
GO

-- 插入示例数据
IF NOT EXISTS (SELECT * FROM [dbo].[AppVersions] WHERE [Platform] = 'Android' AND [VersionNumber] = '1.0.0')
BEGIN
    INSERT INTO [dbo].[AppVersions] 
    ([VersionNumber], [VersionCode], [Platform], [Title], [Description], [UpdateType], [IsForceUpdate], 
     [DownloadUrl], [FileSize], [Status], [TargetAudience], [CreatedBy])
    VALUES 
    ('1.0.0', 1, 'Android', 'CoreHub 初始版本', 
     '这是 CoreHub 移动应用的初始版本，包含以下功能：\n• 设备报修\n• 维修单查看\n• 二维码扫描\n• 消息通知', 
     'Major', 0, 'https://************:8081/updates/CoreHub_1.0.0.apk', 25600000, 'Released', 'All', 1)
    
    PRINT '✓ 插入示例版本数据成功'
END
ELSE
BEGIN
    PRINT '⚠ 示例版本数据已存在，跳过插入'
END
GO

-- 创建更新日志表（可选）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AppUpdateLogs' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AppUpdateLogs](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [VersionId] [int] NOT NULL,
        [DeviceId] [nvarchar](100) NULL,
        [UserId] [int] NULL,
        [Platform] [nvarchar](20) NOT NULL,
        [FromVersion] [nvarchar](20) NULL,
        [ToVersion] [nvarchar](20) NOT NULL,
        [UpdateType] [nvarchar](20) NOT NULL, -- Check, Download, Install, Complete
        [Status] [nvarchar](20) NOT NULL, -- Success, Failed, Cancelled
        [ErrorMessage] [nvarchar](500) NULL,
        [DeviceInfo] [ntext] NULL,
        [CreatedAt] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        CONSTRAINT [PK_AppUpdateLogs] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    -- 创建索引
    CREATE NONCLUSTERED INDEX [IX_AppUpdateLogs_VersionId] ON [dbo].[AppUpdateLogs]([VersionId] ASC)
    CREATE NONCLUSTERED INDEX [IX_AppUpdateLogs_DeviceId] ON [dbo].[AppUpdateLogs]([DeviceId] ASC)
    CREATE NONCLUSTERED INDEX [IX_AppUpdateLogs_CreatedAt] ON [dbo].[AppUpdateLogs]([CreatedAt] DESC)
    
    PRINT '✓ 创建 AppUpdateLogs 表成功'
END
ELSE
BEGIN
    PRINT '⚠ AppUpdateLogs 表已存在，跳过创建'
END
GO

-- 添加外键约束
IF EXISTS (SELECT * FROM sysobjects WHERE name='AppVersions' AND xtype='U') AND 
   EXISTS (SELECT * FROM sysobjects WHERE name='AppUpdateLogs' AND xtype='U')
BEGIN
    IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_AppUpdateLogs_VersionId_AppVersions')
    BEGIN
        ALTER TABLE [dbo].[AppUpdateLogs] ADD CONSTRAINT [FK_AppUpdateLogs_VersionId_AppVersions] 
        FOREIGN KEY([VersionId]) REFERENCES [dbo].[AppVersions] ([Id])
        PRINT '✓ 创建 FK_AppUpdateLogs_VersionId_AppVersions 外键约束成功'
    END
END
GO

-- 创建存储过程：获取最新版本
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetLatestAppVersion')
    DROP PROCEDURE [dbo].[sp_GetLatestAppVersion]
GO

CREATE PROCEDURE [dbo].[sp_GetLatestAppVersion]
    @Platform NVARCHAR(20),
    @TargetAudience NVARCHAR(20) = 'All',
    @DepartmentId INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1 
        [Id], [VersionNumber], [VersionCode], [Platform], [Title], [Description],
        [UpdateType], [IsForceUpdate], [DownloadUrl], [FileSize], [FileMd5],
        [MinSupportedVersionCode], [Status], [TargetAudience], [TargetDepartmentIds],
        [ReleaseTime], [CreatedAt], [CreatedBy], [UpdatedAt], [UpdatedBy], [IsEnabled], [Remark]
    FROM [dbo].[AppVersions]
    WHERE [Platform] = @Platform 
        AND [Status] = 'Released' 
        AND [IsEnabled] = 1
        AND (
            [TargetAudience] = 'All' 
            OR [TargetAudience] = @TargetAudience
            OR (@TargetAudience = 'Department' AND @DepartmentId IS NOT NULL 
                AND [TargetDepartmentIds] IS NOT NULL 
                AND [TargetDepartmentIds] LIKE '%' + CAST(@DepartmentId AS NVARCHAR) + '%')
        )
    ORDER BY [VersionCode] DESC
END
GO

PRINT '✓ 创建 sp_GetLatestAppVersion 存储过程成功'

-- 创建存储过程：记录更新日志
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_LogAppUpdate')
    DROP PROCEDURE [dbo].[sp_LogAppUpdate]
GO

CREATE PROCEDURE [dbo].[sp_LogAppUpdate]
    @VersionId INT,
    @DeviceId NVARCHAR(100) = NULL,
    @UserId INT = NULL,
    @Platform NVARCHAR(20),
    @FromVersion NVARCHAR(20) = NULL,
    @ToVersion NVARCHAR(20),
    @UpdateType NVARCHAR(20),
    @Status NVARCHAR(20),
    @ErrorMessage NVARCHAR(500) = NULL,
    @DeviceInfo NTEXT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO [dbo].[AppUpdateLogs]
    ([VersionId], [DeviceId], [UserId], [Platform], [FromVersion], [ToVersion], 
     [UpdateType], [Status], [ErrorMessage], [DeviceInfo], [CreatedAt])
    VALUES
    (@VersionId, @DeviceId, @UserId, @Platform, @FromVersion, @ToVersion, 
     @UpdateType, @Status, @ErrorMessage, @DeviceInfo, GETUTCDATE())
     
    SELECT SCOPE_IDENTITY() AS LogId
END
GO

PRINT '✓ 创建 sp_LogAppUpdate 存储过程成功'

-- 验证表结构
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('AppVersions', 'AppUpdateLogs')
ORDER BY TABLE_NAME, ORDINAL_POSITION

PRINT ''
PRINT '========================================='
PRINT '应用更新系统数据库脚本执行完成！'
PRINT '========================================='
PRINT '已创建的表：'
PRINT '• AppVersions - 应用版本管理表'
PRINT '• AppUpdateLogs - 更新日志表'
PRINT ''
PRINT '已创建的存储过程：'
PRINT '• sp_GetLatestAppVersion - 获取最新版本'
PRINT '• sp_LogAppUpdate - 记录更新日志'
PRINT ''
-- 添加版本管理菜单项
DECLARE @SystemManagementId INT
SELECT @SystemManagementId = Id FROM MenuItems WHERE Code = 'SystemManagement'

IF @SystemManagementId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'AppUpdateManagement')
BEGIN
    INSERT INTO [dbo].[MenuItems]
    ([Code], [Name], [Description], [RouteUrl], [Icon], [ParentId], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
    VALUES
    ('AppUpdateManagement', '版本管理', '应用版本更新管理', 'app-update-management', 'system_update', @SystemManagementId, 2, 110, 'AppUpdateManagement.View', 1, 0, 1, 1)

    PRINT '✓ 添加版本管理菜单项成功'
END
ELSE IF @SystemManagementId IS NULL
BEGIN
    PRINT '⚠ 未找到系统管理菜单，请先初始化基础菜单数据'
END
ELSE
BEGIN
    PRINT '⚠ 版本管理菜单项已存在，跳过添加'
END
GO

-- 添加版本管理权限
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'AppUpdateManagement.View')
BEGIN
    INSERT INTO [dbo].[Permissions]
    ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsEnabled], [IsSystem])
    VALUES
    ('AppUpdateManagement.View', '查看版本管理', '查看应用版本管理页面', 'AppUpdateManagement', 'View', 2, 110, 1, 1),
    ('AppUpdateManagement.Create', '创建版本', '创建新的应用版本', 'AppUpdateManagement', 'Create', 3, 111, 1, 1),
    ('AppUpdateManagement.Edit', '编辑版本', '编辑应用版本信息', 'AppUpdateManagement', 'Edit', 3, 112, 1, 1),
    ('AppUpdateManagement.Delete', '删除版本', '删除应用版本', 'AppUpdateManagement', 'Delete', 3, 113, 1, 1),
    ('AppUpdateManagement.Publish', '发布版本', '发布应用版本', 'AppUpdateManagement', 'Publish', 3, 114, 1, 1),
    ('AppUpdateManagement.Withdraw', '撤回版本', '撤回已发布的版本', 'AppUpdateManagement', 'Withdraw', 3, 115, 1, 1)

    PRINT '✓ 添加版本管理权限成功'
END
ELSE
BEGIN
    PRINT '⚠ 版本管理权限已存在，跳过添加'
END
GO

-- 为系统管理员角色分配版本管理权限
DECLARE @AdminRoleId INT
SELECT @AdminRoleId = Id FROM Roles WHERE Code = 'Administrator'

IF @AdminRoleId IS NOT NULL
BEGIN
    -- 添加版本管理权限到系统管理员角色
    INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId])
    SELECT @AdminRoleId, p.Id
    FROM Permissions p
    WHERE p.Code LIKE 'AppUpdateManagement.%'
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp
        WHERE rp.RoleId = @AdminRoleId AND rp.PermissionId = p.Id
    )

    PRINT '✓ 为系统管理员角色分配版本管理权限成功'
END
ELSE
BEGIN
    PRINT '⚠ 未找到系统管理员角色，请手动分配权限'
END
GO

PRINT ''
PRINT '下一步操作：'
PRINT '1. 在 wwwroot/updates 目录下放置 APK 文件'
PRINT '2. 通过管理界面创建和发布新版本'
PRINT '3. 客户端将自动检查和下载更新'
PRINT '4. 访问 https://************:8081/app-update-management 进行版本管理'
PRINT '========================================='
