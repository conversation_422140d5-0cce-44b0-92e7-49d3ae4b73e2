{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",

  "ConnectionStrings": {
    "DefaultConnection": "Server=***********;Database=CoreHub;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True"
  },

  "AuthenticationSettings": {
    "UseStoredProcedure": true,
    "MaxLoginFailureCount": 5,
    "AccountLockoutDuration": 30,
    "PasswordComplexity": {
      "MinLength": 6,
      "RequireDigit": false,
      "RequireLowercase": false,
      "RequireUppercase": false,
      "RequireNonAlphanumeric": false
    }
  },

  "Serilog": {
    "Using": [ "Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Seq" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "Microsoft.EntityFrameworkCore": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/corehub-web-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "fileSizeLimitBytes": ********,
          "rollOnFileSizeLimit": true,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "Seq",
        "Args": {
          "serverUrl": "http://localhost:5341"
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithProcessId", "WithThreadId", "WithEnvironmentName" ],
    "Properties": {
      "Application": "CoreHub.Web",
      "Environment": "Development"
    }
  },
//
  "Kestrel": {
    "EndPoints": {
      "Http": {
        "Url": "http://0.0.0.0:8080"
      },
      "Https": {
        "Url": "https://0.0.0.0:8081",
        "Certificate": {
          "Path": "Certificates/api_saintyeartex_com.pfx",
          "Password": "HO4LokI5cBvEuKYT"
        },
        "Protocols": "Http1AndHttp2",
        "SslProtocols": ["Tls12", "Tls13"]
      }
    }
  },
  "CertificateMonitoring": {
    "Enabled": true,
    "CheckIntervalHours": 24,
    "WarningDays": 30,
    "CriticalDays": 7,
    "EnableEmailNotification": false,
    "NotificationEmails": [
      "<EMAIL>"
    ]
  },
  "UpdateService": {
    "BaseUrl": "https://************:8081",
    "UpdatesDirectory": "wwwroot/updates",
    "MaxFileSize": ********0,
    "AllowedFileTypes": [ ".apk", ".ipa", ".msix", ".dmg" ]
  }
}
