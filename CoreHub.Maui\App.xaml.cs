﻿using CoreHub.Shared.Services;

namespace CoreHub
{
    public partial class App : Application
    {
        public App()
        {
            try
            {
                InitializeComponent();

                // 设置全局异常处理
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                MainPage = new MainPage();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"App Constructor Error: {ex}");
                throw;
            }
        }

        protected override async void OnStart()
        {
            base.OnStart();

            // 启动自动更新检查
            await StartUpdateCheckAsync();
        }

        private async Task StartUpdateCheckAsync()
        {
            try
            {
#if ANDROID
                // 获取更新服务并启动自动检查
                var serviceProvider = Handler?.MauiContext?.Services;
                if (serviceProvider != null)
                {
                    var updateService = serviceProvider.GetService<IClientUpdateService>();
                    if (updateService != null)
                    {
                        // 启动自动更新检查（每24小时检查一次）
                        updateService.StartAutoUpdateCheck();

                        // 应用启动时立即检查一次更新（静默检查）
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await Task.Delay(3000); // 延迟3秒，等待应用完全启动
                                await updateService.CheckForUpdateAsync(silent: true);
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"启动时检查更新失败: {ex.Message}");
                            }
                        });
                    }
                }
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启动更新检查失败: {ex.Message}");
            }
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"Unhandled Exception: {exception}");

            // 在这里可以添加错误报告或日志记录
        }

        private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"Unobserved Task Exception: {e.Exception}");
            e.SetObserved(); // 防止应用崩溃
        }
    }
}
