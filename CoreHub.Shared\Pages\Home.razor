@page "/"
@using CoreHub.Shared.Services
@inject NavigationManager Navigation
@inject IAppUpdateService UpdateService
@inject ISnackbar Snackbar
@inject IServiceProvider ServiceProvider

<PageTitle>首页</PageTitle>

<style>
    .mud-picker-popup {
        z-index: 9999 !important;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .date-picker-container {
        overflow: visible !important;
        position: relative;
    }
</style>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h3" Class="mb-4">欢迎使用设备管理系统</MudText>
    
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h5" GutterBottom="true">系统功能</MudText>
        <MudText Typo="Typo.body1" Class="mb-3">
            这是一个基于.NET 8 MAUI Blazor Hybrid的跨平台设备管理系统。
            您可以使用二维码扫描功能来快速识别和管理设备。
        </MudText>
        
        <MudGrid>
            <MudItem xs="12" sm="6" md="4">
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="NavigateToDeviceScanner"
                           StartIcon="@Icons.Material.Filled.QrCodeScanner"
                           FullWidth="true">
                    开始扫描设备
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudButton Variant="Variant.Filled"
                           Color="Color.Secondary"
                           OnClick="TestAppUpdate"
                           StartIcon="@Icons.Material.Filled.SystemUpdate"
                           FullWidth="true"
                           Disabled="@isCheckingUpdate">
                    @if (isCheckingUpdate)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">检查中...</span>
                    }
                    else
                    {
                        <span>测试应用更新</span>
                    }
                </MudButton>
            </MudItem>
            <MudItem xs="12" sm="6" md="4">
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Info"
                           OnClick="NavigateToVersionManagement"
                           StartIcon="@Icons.Material.Filled.Settings"
                           FullWidth="true">
                    版本管理
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 日期选择器演示 -->
    <MudPaper Class="pa-4" Style="min-height: 400px;">
        <MudText Typo="Typo.h6" GutterBottom="true">日期选择器演示</MudText>
        <MudGrid Class="date-picker-container">
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 1" 
                              @bind-Date="selectedDate1" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 2" 
                              @bind-Date="selectedDate2" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
            <MudItem xs="12" md="4" Class="mb-4">
                <MudDatePicker Label="选择日期 3" 
                              @bind-Date="selectedDate3" 
                              Elevation="8"
                              PickerVariant="PickerVariant.Dialog"
                              Culture="@System.Globalization.CultureInfo.GetCultureInfo("zh-CN")" />
            </MudItem>
        </MudGrid>
        
        <!-- 显示选择的日期 -->
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.subtitle1">已选择的日期：</MudText>
        <MudText Typo="Typo.body2">日期 1: @(selectedDate1?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
        <MudText Typo="Typo.body2">日期 2: @(selectedDate2?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
        <MudText Typo="Typo.body2">日期 3: @(selectedDate3?.ToString("yyyy-MM-dd") ?? "未选择")</MudText>
    </MudPaper>
</MudContainer>

@code {
    private DateTime? selectedDate1;
    private DateTime? selectedDate2;
    private DateTime? selectedDate3;
    private bool isCheckingUpdate = false;

    private void NavigateToDeviceScanner()
    {
        Navigation.NavigateTo("/devicescanner");
    }

    private void NavigateToVersionManagement()
    {
        Navigation.NavigateTo("/app-update-management");
    }

    private async Task TestAppUpdate()
    {
        try
        {
            isCheckingUpdate = true;
            StateHasChanged();

            // 检查更新
            Snackbar.Add("开始检查应用更新...", Severity.Info);

            // 尝试获取更新UI服务（仅在MAUI平台可用）
            var updateUIService = ServiceProvider.GetService<IUpdateUIService>();
            if (updateUIService != null)
            {
                Snackbar.Add("使用专门的更新界面...", Severity.Info);
                try
                {
                    await updateUIService.CheckForUpdateAsync();
                    return; // 使用了新的更新界面，直接返回
                }
                catch (Exception ex)
                {
                    Snackbar.Add($"更新界面调用失败: {ex.Message}", Severity.Error);
                }
            }
            else
            {
                Snackbar.Add("更新UI服务未注册，使用传统更新方式", Severity.Info);
            }

            // 尝试获取客户端更新服务（仅在MAUI平台可用）
            var clientUpdateService = ServiceProvider.GetService(typeof(CoreHub.Shared.Services.IClientUpdateService)) as CoreHub.Shared.Services.IClientUpdateService;
            if (clientUpdateService != null)
            {
                Snackbar.Add("找到客户端更新服务，开始真实更新流程...", Severity.Info);
                var updateResponse = await clientUpdateService.CheckForUpdateAsync(silent: false);
                Snackbar.Add($"更新检查完成: HasUpdate={updateResponse.HasUpdate}, LatestVersion={updateResponse.LatestVersion?.VersionNumber}", Severity.Info);

                if (updateResponse.HasUpdate && updateResponse.LatestVersion != null)
                {
                    Snackbar.Add($"发现最新版本: {updateResponse.LatestVersion.VersionNumber} (版本代码: {updateResponse.LatestVersion.VersionCode})", Severity.Success);
                    Snackbar.Add("请使用顶部工具栏的更新按钮来使用新的更新界面", Severity.Info);
                }
                else
                {
                    Snackbar.Add("当前已是最新版本", Severity.Info);
                }
            }
            else
            {
                Snackbar.Add("客户端更新服务不可用，使用Web版本检查...", Severity.Warning);

                // 在Web平台上只显示版本信息
                var latestVersion = await UpdateService.GetLatestVersionAsync("Android");

                if (latestVersion != null)
                {
                    Snackbar.Add($"发现最新版本: {latestVersion.VersionNumber} (版本代码: {latestVersion.VersionCode})", Severity.Success);

                    // 显示版本详情
                    var message = $"版本号: {latestVersion.VersionNumber} | " +
                                 $"版本代码: {latestVersion.VersionCode} | " +
                                 $"标题: {latestVersion.Title} | " +
                                 $"文件大小: {FormatFileSize(latestVersion.FileSize)} | " +
                                 $"创建时间: {latestVersion.CreatedAt:yyyy-MM-dd HH:mm} | " +
                                 $"强制更新: {(latestVersion.IsForceUpdate ? "是" : "否")}";

                    Snackbar.Add(message, Severity.Info);

                    if (latestVersion.IsForceUpdate)
                    {
                        Snackbar.Add("⚠️ 这是强制更新版本！", Severity.Warning);
                    }
                }
                else
                {
                    Snackbar.Add("当前已是最新版本", Severity.Info);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"检查更新失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isCheckingUpdate = false;
            StateHasChanged();
        }
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

