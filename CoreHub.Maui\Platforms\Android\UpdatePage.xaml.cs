using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;
using System.Diagnostics;

namespace CoreHub.Platforms.Android;

public partial class UpdatePage : ContentPage
{
    private readonly IClientUpdateService _updateService;
    private readonly IApplicationLogger _logger;
    private UpdateCheckResponse? _updateResponse;
    private CancellationTokenSource? _downloadCancellationTokenSource;
    private bool _isDownloading = false;
    private DateTime _downloadStartTime;
    private long _lastBytesReceived = 0;
    private DateTime _lastSpeedUpdateTime;

    public UpdatePage(IClientUpdateService updateService, IApplicationLogger logger)
    {
        InitializeComponent();
        _updateService = updateService;
        _logger = logger;
        
        // 订阅下载进度事件
        _updateService.DownloadProgressChanged += OnDownloadProgressChanged;
        _updateService.DownloadCompleted += OnDownloadCompleted;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await LoadUpdateInfoAsync();
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();

        // 强制更新时不允许退出页面
        if (_updateResponse?.IsForceUpdate == true && _isDownloading)
        {
            return;
        }

        // 取消下载
        _downloadCancellationTokenSource?.Cancel();

        // 取消订阅事件
        _updateService.DownloadProgressChanged -= OnDownloadProgressChanged;
        _updateService.DownloadCompleted -= OnDownloadCompleted;
    }

    protected override bool OnBackButtonPressed()
    {
        // 强制更新时不允许返回
        if (_updateResponse?.IsForceUpdate == true)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await DisplayAlert("提示", "强制更新进行中，无法退出", "确定");
            });
            return true; // 阻止返回
        }

        return base.OnBackButtonPressed();
    }

    private async Task LoadUpdateInfoAsync()
    {
        try
        {
            // 检查更新
            _updateResponse = await _updateService.CheckForUpdateAsync(silent: false);
            
            if (_updateResponse?.HasUpdate == true && _updateResponse.LatestVersion != null)
            {
                // 显示版本信息
                var currentVersion = await GetCurrentVersionAsync();
                CurrentVersionLabel.Text = currentVersion;
                LatestVersionLabel.Text = _updateResponse.LatestVersion.VersionNumber;
                FileSizeLabel.Text = FormatBytes(_updateResponse.LatestVersion.FileSize);
                ReleaseDateLabel.Text = _updateResponse.LatestVersion.ReleaseTime?.ToString("yyyy-MM-dd") ?? "未知";

                // 显示更新说明
                ReleaseNotesLabel.Text = string.IsNullOrWhiteSpace(_updateResponse.LatestVersion.Description)
                    ? "暂无更新说明"
                    : _updateResponse.LatestVersion.Description;
                
                // 处理强制更新
                if (_updateResponse.IsForceUpdate)
                {
                    ForceUpdateFrame.IsVisible = true;
                    CancelButton.IsVisible = false;
                    UpdateButton.Text = "必须更新";

                    _logger.LogInformation("检测到强制更新，将自动开始下载");

                    // 强制更新时自动开始下载
                    Dispatcher.Dispatch(async () =>
                    {
                        await Task.Delay(1000); // 等待UI渲染完成
                        await StartDownloadAsync();
                    });
                }
                else
                {
                    ForceUpdateFrame.IsVisible = false;
                    CancelButton.IsVisible = true;
                    UpdateButton.Text = "立即更新";
                }
            }
            else
            {
                // 没有更新可用
                await DisplayAlert("提示", "当前已是最新版本", "确定");
                await Navigation.PopAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载更新信息时发生异常");
            await DisplayAlert("错误", $"检查更新失败：{ex.Message}", "确定");
            await Navigation.PopAsync();
        }
    }

    private async Task<string> GetCurrentVersionAsync()
    {
        try
        {
            return AppInfo.VersionString;
        }
        catch
        {
            return "未知";
        }
    }

    private async void OnUpdateClicked(object sender, EventArgs e)
    {
        if (_isDownloading)
        {
            // 强制更新时不允许取消下载
            if (_updateResponse?.IsForceUpdate == true)
            {
                await DisplayAlert("提示", "强制更新不能取消，请等待下载完成", "确定");
                return;
            }

            // 如果正在下载，取消下载
            _downloadCancellationTokenSource?.Cancel();
            return;
        }

        await StartDownloadAsync();
    }

    private async void OnCancelClicked(object sender, EventArgs e)
    {
        if (_updateResponse?.IsForceUpdate == true)
        {
            // 强制更新不允许取消
            return;
        }

        await Navigation.PopAsync();
    }

    private async Task StartDownloadAsync()
    {
        if (_updateResponse?.LatestVersion == null || _isDownloading)
            return;

        try
        {
            _isDownloading = true;
            _downloadStartTime = DateTime.Now;
            _lastBytesReceived = 0;
            _lastSpeedUpdateTime = DateTime.Now;

            // 更新UI状态
            if (_updateResponse?.IsForceUpdate == true)
            {
                UpdateButton.Text = "正在下载...";
                UpdateButton.BackgroundColor = Color.FromArgb("#6C757D"); // 灰色，表示不可操作
                UpdateButton.IsEnabled = false;
            }
            else
            {
                UpdateButton.Text = "取消下载";
                UpdateButton.BackgroundColor = Colors.Red;
            }

            ProgressFrame.IsVisible = true;
            ProgressLabel.Text = "开始下载...";
            ProgressPercentLabel.Text = "0%";
            DownloadProgressBar.Progress = 0;

            // 创建取消令牌
            _downloadCancellationTokenSource = new CancellationTokenSource();

            // 开始下载
            var progress = new Progress<DownloadProgress>(OnDownloadProgress);
            var result = await _updateService.DownloadUpdateAsync(
                _updateResponse.LatestVersion, 
                progress, 
                _downloadCancellationTokenSource.Token);

            if (result.IsSuccess && !string.IsNullOrEmpty(result.FilePath))
            {
                // 下载成功，开始安装
                await InstallUpdateAsync(result.FilePath);
            }
            else if (!_downloadCancellationTokenSource.Token.IsCancellationRequested)
            {
                // 下载失败
                await DisplayAlert("下载失败", result.ErrorMessage ?? "未知错误", "确定");
                ResetDownloadUI();
            }
        }
        catch (OperationCanceledException)
        {
            // 用户取消下载
            _logger.LogInformation("用户取消了更新下载");
            ResetDownloadUI();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载更新时发生异常");
            await DisplayAlert("下载失败", $"下载过程中发生错误：{ex.Message}", "确定");
            ResetDownloadUI();
        }
        finally
        {
            _isDownloading = false;
        }
    }

    private void OnDownloadProgress(DownloadProgress progress)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                // 更新进度条
                DownloadProgressBar.Progress = progress.ProgressPercentage / 100.0;
                ProgressPercentLabel.Text = $"{progress.ProgressPercentage:F1}%";
                
                // 更新状态文本
                ProgressLabel.Text = $"已下载 {FormatBytes(progress.BytesReceived)} / {FormatBytes(progress.TotalBytesToReceive)}";
                
                // 计算下载速度
                var now = DateTime.Now;
                if ((now - _lastSpeedUpdateTime).TotalSeconds >= 1.0) // 每秒更新一次速度
                {
                    var bytesPerSecond = (progress.BytesReceived - _lastBytesReceived) / (now - _lastSpeedUpdateTime).TotalSeconds;
                    DownloadSpeedLabel.Text = $"下载速度: {FormatBytes((long)bytesPerSecond)}/s";
                    
                    _lastBytesReceived = progress.BytesReceived;
                    _lastSpeedUpdateTime = now;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新下载进度UI时发生异常");
            }
        });
    }

    private void OnDownloadProgressChanged(object? sender, DownloadProgress progress)
    {
        OnDownloadProgress(progress);
    }

    private void OnDownloadCompleted(object? sender, DownloadCompletedEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            if (e.IsSuccess && !string.IsNullOrEmpty(e.FilePath))
            {
                await InstallUpdateAsync(e.FilePath);
            }
            else
            {
                await DisplayAlert("下载失败", e.ErrorMessage ?? "未知错误", "确定");
                ResetDownloadUI();
            }
        });
    }

    private async Task InstallUpdateAsync(string filePath)
    {
        try
        {
            ProgressLabel.Text = "准备安装...";
            ProgressPercentLabel.Text = "100%";
            DownloadSpeedLabel.Text = "下载完成";

            // 安装更新
            var result = await _updateService.InstallUpdateAsync(filePath);
            
            if (!result.IsSuccess)
            {
                await DisplayAlert("安装失败", result.ErrorMessage ?? "未知错误", "确定");
                ResetDownloadUI();
            }
            // 如果安装成功，系统会启动安装程序，应用可能会被关闭
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "安装更新时发生异常");
            await DisplayAlert("安装失败", $"安装过程中发生错误：{ex.Message}", "确定");
            ResetDownloadUI();
        }
    }

    private void ResetDownloadUI()
    {
        _isDownloading = false;

        if (_updateResponse?.IsForceUpdate == true)
        {
            UpdateButton.Text = "必须更新";
            UpdateButton.BackgroundColor = Color.FromArgb("#007ACC");
            UpdateButton.IsEnabled = true;
        }
        else
        {
            UpdateButton.Text = "立即更新";
            UpdateButton.BackgroundColor = Color.FromArgb("#007ACC");
            UpdateButton.IsEnabled = true;
        }

        ProgressFrame.IsVisible = false;
        _downloadCancellationTokenSource?.Dispose();
        _downloadCancellationTokenSource = null;
    }

    private static string FormatBytes(long bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB" };
        int counter = 0;
        decimal number = bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }
}
