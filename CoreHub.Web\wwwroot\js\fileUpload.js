// 文件上传功能
window.uploadFile = async (file, platform, version) => {
    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('platform', platform);
        formData.append('version', version);

        const response = await fetch('/api/AppUpdate/upload', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            return result;
        } else {
            const errorText = await response.text();
            throw new Error(`上传失败: ${errorText}`);
        }
    } catch (error) {
        console.error('文件上传错误:', error);
        throw error;
    }
};

// 格式化文件大小
window.formatFileSize = (bytes) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};
