using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            // 忽略SSL证书验证（仅用于测试）
            var handler = new HttpClientHandler()
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };
            
            using var testClient = new HttpClient(handler);
            testClient.BaseAddress = new Uri("https://************:8081");
            
            Console.WriteLine("测试更新下载功能...");
            
            // 1. 测试检查更新API
            Console.WriteLine("\n1. 测试检查更新API...");
            var checkResponse = await testClient.GetAsync("/api/AppUpdate/check/android/1.0.0");
            Console.WriteLine($"检查更新响应状态: {checkResponse.StatusCode}");
            
            if (checkResponse.IsSuccessStatusCode)
            {
                var content = await checkResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"响应内容: {content}");
                
                // 解析响应
                var versionInfo = JsonSerializer.Deserialize<VersionInfo>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                if (versionInfo != null && versionInfo.HasUpdate)
                {
                    Console.WriteLine($"发现新版本: {versionInfo.VersionNumber}");
                    Console.WriteLine($"下载URL: {versionInfo.DownloadUrl}");
                    
                    // 2. 测试下载功能
                    Console.WriteLine("\n2. 测试下载功能...");
                    
                    string downloadUrl;
                    if (Uri.IsWellFormedUriString(versionInfo.DownloadUrl, UriKind.Absolute))
                    {
                        downloadUrl = versionInfo.DownloadUrl;
                    }
                    else
                    {
                        downloadUrl = versionInfo.DownloadUrl.StartsWith("/") ? versionInfo.DownloadUrl : "/" + versionInfo.DownloadUrl;
                    }
                    
                    Console.WriteLine($"尝试下载: {downloadUrl}");
                    
                    var downloadResponse = await testClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead);
                    Console.WriteLine($"下载响应状态: {downloadResponse.StatusCode}");
                    
                    if (downloadResponse.IsSuccessStatusCode)
                    {
                        var contentLength = downloadResponse.Content.Headers.ContentLength;
                        Console.WriteLine($"文件大小: {contentLength} bytes");
                        Console.WriteLine("✅ 下载测试成功！");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 下载失败: {downloadResponse.StatusCode}");
                        var errorContent = await downloadResponse.Content.ReadAsStringAsync();
                        Console.WriteLine($"错误内容: {errorContent}");
                    }
                }
                else
                {
                    Console.WriteLine("没有可用的更新");
                }
            }
            else
            {
                Console.WriteLine($"❌ 检查更新失败: {checkResponse.StatusCode}");
                var errorContent = await checkResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"错误内容: {errorContent}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}

public class VersionInfo
{
    public bool HasUpdate { get; set; }
    public string VersionNumber { get; set; } = string.Empty;
    public string DownloadUrl { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FormattedFileSize { get; set; } = string.Empty;
    public string Md5Hash { get; set; } = string.Empty;
    public DateTime ReleaseDate { get; set; }
    public string ReleaseNotes { get; set; } = string.Empty;
}
