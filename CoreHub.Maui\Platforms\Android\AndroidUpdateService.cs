using Android.Content;
using Android.Content.PM;
using AndroidX.Core.Content;
using CoreHub.Shared.Models.AppUpdate;
using CoreHub.Shared.Services;
using System.Security.Cryptography;
using System.Text.Json;
using Android.Net;
using Android.OS;
using AndroidX.Core.App;
using Android.App;

namespace CoreHub.Platforms.Android
{
    /// <summary>
    /// Android平台更新服务实现
    /// </summary>
    public class AndroidUpdateService : IClientUpdateService
    {
        private readonly HttpClient _httpClient;
        private readonly IApplicationLogger _logger;
        private readonly string _baseUrl;
        private readonly string _downloadPath;
        private Timer? _autoCheckTimer;
        private int _checkIntervalHours = 24; // 默认24小时检查一次

        public event EventHandler<UpdateCheckResponse>? UpdateAvailable;
        public event EventHandler<DownloadProgress>? DownloadProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;

        /// <summary>
        /// 显示更新页面
        /// </summary>
        public async Task ShowUpdatePageAsync()
        {
            try
            {
                _logger.LogInformation("开始显示更新页面");

                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    _logger.LogInformation("在主线程中创建更新页面");

                    var app = Microsoft.Maui.Controls.Application.Current;
                    if (app == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Application.Current 为 null");
                        return;
                    }

                    var mainPage = app.MainPage;
                    if (mainPage == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Application.Current.MainPage 为 null");
                        return;
                    }

                    var navigation = mainPage.Navigation;
                    if (navigation == null)
                    {
                        _logger.LogError(new InvalidOperationException(), "Navigation 为 null");
                        return;
                    }

                    _logger.LogInformation("创建UpdatePage实例");
                    var updatePage = new UpdatePage(this, _logger);

                    _logger.LogInformation("检查MainPage类型: {MainPageType}", mainPage.GetType().Name);

                    // 检查MainPage是否是NavigationPage
                    if (mainPage is NavigationPage navigationPage)
                    {
                        _logger.LogInformation("MainPage是NavigationPage，使用PushAsync");
                        await navigationPage.PushAsync(updatePage);
                        _logger.LogInformation("更新页面已成功推送到NavigationPage");
                    }
                    else
                    {
                        _logger.LogInformation("MainPage不是NavigationPage，直接替换MainPage");
                        // 如果MainPage不是NavigationPage，我们需要创建一个新的NavigationPage
                        var newNavigationPage = new NavigationPage(updatePage);
                        app.MainPage = newNavigationPage;
                        _logger.LogInformation("已将更新页面设置为新的MainPage");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示更新页面时发生异常: {Message}", ex.Message);
            }
        }

        public AndroidUpdateService(HttpClient httpClient, IApplicationLogger logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = "https://************:8081"; // 硬编码基础URL，也可以通过配置注入

            // 设置HttpClient的BaseAddress以支持相对URL
            if (_httpClient.BaseAddress == null)
            {
                _httpClient.BaseAddress = new System.Uri(_baseUrl);
            }

            // 设置下载目录
            var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
            _downloadPath = Path.Combine(context.GetExternalFilesDir(null)?.AbsolutePath ?? context.FilesDir.AbsolutePath, "updates");

            // 确保下载目录存在
            Directory.CreateDirectory(_downloadPath);
        }

        /// <summary>
        /// 检查更新
        /// </summary>
        public async Task<UpdateCheckResponse> CheckForUpdateAsync(bool silent = false)
        {
            try
            {
                var (currentVersion, currentVersionCode) = await GetCurrentVersionAsync();
                var deviceInfo = await GetDeviceInfoAsync();

                var request = new UpdateCheckRequest
                {
                    CurrentVersion = currentVersion,
                    CurrentVersionCode = currentVersionCode,
                    Platform = "Android",
                    DeviceId = GetDeviceId(),
                    Device = deviceInfo
                };

                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/AppUpdate/check", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var updateResponse = JsonSerializer.Deserialize<UpdateCheckResponse>(responseJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (updateResponse != null)
                    {
                        _logger.LogInformation("更新检查完成: 有更新={HasUpdate}, 强制更新={IsForceUpdate}, 策略={Strategy}, 静默={Silent}",
                            updateResponse.HasUpdate, updateResponse.IsForceUpdate, updateResponse.Strategy, silent);

                        if (updateResponse.HasUpdate)
                        {
                            // 总是触发更新可用事件，让订阅者决定如何处理
                            UpdateAvailable?.Invoke(this, updateResponse);

                            // 如果是强制更新，直接显示更新页面
                            if (updateResponse.IsForceUpdate)
                            {
                                _logger.LogInformation("检测到强制更新，显示更新页面");
                                await ShowUpdatePageAsync();
                            }
                            // 如果是静默检查且有更新，显示通知
                            else if (silent)
                            {
                                _logger.LogInformation("静默检查发现更新，显示通知");
                                await ShowUpdateNotificationAsync(updateResponse);
                            }
                            else
                            {
                                _logger.LogInformation("非静默检查发现可选更新，不自动显示页面");
                            }
                        }

                        return updateResponse;
                    }
                }

                _logger.LogWarning("更新检查失败: HTTP {StatusCode}", response.StatusCode);
                return new UpdateCheckResponse { HasUpdate = false, Message = "检查更新失败" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查更新时发生异常");
                return new UpdateCheckResponse { HasUpdate = false, Message = "检查更新失败" };
            }
        }

        /// <summary>
        /// 下载更新
        /// </summary>
        public async Task<(bool IsSuccess, string? FilePath, string? ErrorMessage)> DownloadUpdateAsync(
            VersionInfo versionInfo, 
            IProgress<DownloadProgress>? progress = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var fileName = $"CoreHub_{versionInfo.VersionNumber}.apk";
                var filePath = Path.Combine(_downloadPath, fileName);

                // 如果文件已存在且完整，直接返回
                if (File.Exists(filePath))
                {
                    if (!string.IsNullOrEmpty(versionInfo.FileMd5) && 
                        await ValidateUpdateFileAsync(filePath, versionInfo.FileMd5))
                    {
                        _logger.LogInformation("更新文件已存在且完整: {FilePath}", filePath);
                        return (true, filePath, null);
                    }
                    
                    // 文件损坏，删除重新下载
                    File.Delete(filePath);
                }

                _logger.LogInformation("开始下载更新: {VersionNumber}, 大小: {FileSize}",
                    versionInfo.VersionNumber, versionInfo.FormattedFileSize);

                // 构建完整的下载URL
                string downloadUrl;
                if (System.Uri.IsWellFormedUriString(versionInfo.DownloadUrl, UriKind.Absolute))
                {
                    // 如果是绝对URL，直接使用
                    downloadUrl = versionInfo.DownloadUrl;
                }
                else
                {
                    // 如果是相对URL，与BaseAddress组合
                    downloadUrl = versionInfo.DownloadUrl.StartsWith("/") ? versionInfo.DownloadUrl : "/" + versionInfo.DownloadUrl;
                }

                _logger.LogInformation("下载URL: {DownloadUrl}", downloadUrl);

                var response = await _httpClient.GetAsync(downloadUrl, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

                try
                {
                    if (!response.IsSuccessStatusCode)
                    {
                        return (false, null, $"下载失败: HTTP {response.StatusCode}");
                    }

                    var totalBytes = response.Content.Headers.ContentLength ?? versionInfo.FileSize;
                    var downloadProgress = new DownloadProgress { TotalBytesToReceive = totalBytes };

                    // 先读取所有内容到内存，避免流被过早释放
                    var contentBytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);

                    // 写入文件
                    using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

                    var totalBytesRead = 0L;
                    var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                    var bufferSize = 8192;

                    for (int offset = 0; offset < contentBytes.Length; offset += bufferSize)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var bytesToWrite = Math.Min(bufferSize, contentBytes.Length - offset);
                        await fileStream.WriteAsync(contentBytes, offset, bytesToWrite, cancellationToken);
                        totalBytesRead += bytesToWrite;

                        // 更新进度
                        if (progress != null && stopwatch.ElapsedMilliseconds > 100) // 每100ms更新一次进度
                        {
                            downloadProgress.BytesReceived = totalBytesRead;
                            downloadProgress.BytesPerSecond = stopwatch.ElapsedMilliseconds > 0 ?
                                (long)(totalBytesRead * 1000.0 / stopwatch.ElapsedMilliseconds) : 0;

                            if (downloadProgress.BytesPerSecond > 0)
                            {
                                var remainingBytes = totalBytes - totalBytesRead;
                                downloadProgress.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / downloadProgress.BytesPerSecond);
                            }

                            progress.Report(downloadProgress);
                            DownloadProgressChanged?.Invoke(this, downloadProgress);
                            stopwatch.Restart();
                        }
                    }
                }
                finally
                {
                    response?.Dispose();
                }

                // 验证下载的文件
                if (!string.IsNullOrEmpty(versionInfo.FileMd5))
                {
                    var isValid = await ValidateUpdateFileAsync(filePath, versionInfo.FileMd5);
                    if (!isValid)
                    {
                        File.Delete(filePath);
                        var errorMsg = "下载的文件完整性验证失败";
                        _logger.LogError(new Exception(errorMsg), errorMsg);
                        
                        DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
                        {
                            IsSuccess = false,
                            ErrorMessage = errorMsg,
                            VersionInfo = versionInfo
                        });
                        
                        return (false, null, errorMsg);
                    }
                }

                _logger.LogInformation("更新下载完成: {FilePath}", filePath);
                
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
                {
                    IsSuccess = true,
                    FilePath = filePath,
                    VersionInfo = versionInfo
                });

                return (true, filePath, null);
            }
            catch (System.OperationCanceledException)
            {
                _logger.LogInformation("更新下载已取消");
                
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
                {
                    IsSuccess = false,
                    IsCancelled = true,
                    VersionInfo = versionInfo
                });
                
                return (false, null, "下载已取消");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载更新时发生异常");
                
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    VersionInfo = versionInfo
                });
                
                return (false, null, ex.Message);
            }
        }

        /// <summary>
        /// 安装更新
        /// </summary>
        public async Task<(bool IsSuccess, string? ErrorMessage)> InstallUpdateAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return (false, "安装文件不存在");
                }

                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                
                // 检查是否有安装权限
                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    var packageManager = context.PackageManager;
                    if (packageManager != null && !packageManager.CanRequestPackageInstalls())
                    {
                        // 请求安装权限
                        var intent = new Intent(global::Android.Provider.Settings.ActionManageUnknownAppSources);
                        intent.SetData(global::Android.Net.Uri.Parse($"package:{context.PackageName}"));
                        intent.SetFlags(ActivityFlags.NewTask);
                        context.StartActivity(intent);
                        
                        return (false, "需要授予安装未知来源应用的权限");
                    }
                }

                // 创建安装Intent
                var installIntent = new Intent(Intent.ActionInstallPackage);
                installIntent.SetFlags(ActivityFlags.NewTask | ActivityFlags.GrantReadUriPermission);

                // 使用FileProvider获取安全的URI
                var authority = $"{context.PackageName}.fileprovider";
                var apkUri = AndroidX.Core.Content.FileProvider.GetUriForFile(context, authority, new Java.IO.File(filePath));
                installIntent.SetDataAndType(apkUri, "application/vnd.android.package-archive");

                context.StartActivity(installIntent);

                _logger.LogInformation("启动应用安装: {FilePath}", filePath);
                return (true, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安装更新时发生异常: {FilePath}", filePath);
                return (false, ex.Message);
            }
        }

        /// <summary>
        /// 验证更新文件
        /// </summary>
        public async Task<bool> ValidateUpdateFileAsync(string filePath, string expectedMd5)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                using var md5 = MD5.Create();
                using var stream = File.OpenRead(filePath);
                var hash = await Task.Run(() => md5.ComputeHash(stream));
                var actualMd5 = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();

                return string.Equals(actualMd5, expectedMd5, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证文件时发生异常: {FilePath}", filePath);
                return false;
            }
        }

        /// <summary>
        /// 获取当前应用版本信息
        /// </summary>
        public async Task<(string VersionNumber, int VersionCode)> GetCurrentVersionAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                    var packageManager = context.PackageManager;
                    var packageInfo = packageManager?.GetPackageInfo(context.PackageName!, 0);

                    if (packageInfo != null)
                    {
                        var versionName = packageInfo.VersionName ?? "1.0.0";
                        var versionCode = Build.VERSION.SdkInt >= BuildVersionCodes.P ? 
                            (int)packageInfo.LongVersionCode : packageInfo.VersionCode;

                        return (versionName, versionCode);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取应用版本信息时发生异常");
                }

                return ("1.0.0", 1);
            });
        }

        /// <summary>
        /// 获取设备信息
        /// </summary>
        public async Task<CoreHub.Shared.Models.AppUpdate.DeviceInfo> GetDeviceInfoAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo
                    {
                        Model = $"{Build.Manufacturer} {Build.Model}",
                        OsVersion = Build.VERSION.Release,
                        Language = Java.Util.Locale.Default?.Language ?? "zh",
                        TimeZone = Java.Util.TimeZone.Default?.ID ?? "Asia/Shanghai"
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取设备信息时发生异常");
                    return new CoreHub.Shared.Models.AppUpdate.DeviceInfo();
                }
            });
        }

        /// <summary>
        /// 清理旧的更新文件
        /// </summary>
        public async Task<bool> CleanupOldUpdatesAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (!Directory.Exists(_downloadPath))
                    {
                        return true;
                    }

                    var files = Directory.GetFiles(_downloadPath, "*.apk");
                    var cutoffTime = DateTime.Now.AddDays(-7); // 删除7天前的文件

                    foreach (var file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffTime)
                        {
                            File.Delete(file);
                            _logger.LogInformation("清理旧更新文件: {FilePath}", file);
                        }
                    }

                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "清理旧更新文件时发生异常");
                    return false;
                }
            });
        }

        /// <summary>
        /// 设置更新检查间隔
        /// </summary>
        public void SetUpdateCheckInterval(int intervalHours)
        {
            _checkIntervalHours = Math.Max(1, intervalHours); // 最少1小时
            
            // 如果定时器正在运行，重新启动
            if (_autoCheckTimer != null)
            {
                StopAutoUpdateCheck();
                StartAutoUpdateCheck();
            }
        }

        /// <summary>
        /// 启动自动更新检查
        /// </summary>
        public void StartAutoUpdateCheck()
        {
            StopAutoUpdateCheck();

            var interval = TimeSpan.FromHours(_checkIntervalHours);
            _autoCheckTimer = new Timer(async _ =>
            {
                try
                {
                    await CheckForUpdateAsync(silent: true);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "自动检查更新时发生异常");
                }
            }, null, TimeSpan.Zero, interval);

            _logger.LogInformation("启动自动更新检查，间隔: {IntervalHours} 小时", _checkIntervalHours);
        }

        /// <summary>
        /// 停止自动更新检查
        /// </summary>
        public void StopAutoUpdateCheck()
        {
            _autoCheckTimer?.Dispose();
            _autoCheckTimer = null;
            _logger.LogInformation("停止自动更新检查");
        }

        /// <summary>
        /// 获取设备ID
        /// </summary>
        private string GetDeviceId()
        {
            try
            {
                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                return global::Android.Provider.Settings.Secure.GetString(context.ContentResolver, global::Android.Provider.Settings.Secure.AndroidId) ?? "unknown";
            }
            catch
            {
                return "unknown";
            }
        }

        /// <summary>
        /// 显示更新通知
        /// </summary>
        private async Task ShowUpdateNotificationAsync(UpdateCheckResponse updateResponse)
        {
            try
            {
                if (updateResponse.LatestVersion == null) return;

                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;

                // 创建通知渠道
                var channelId = "app_update_channel";
                var channelName = "应用更新";

                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    var notificationManagerCompat = NotificationManagerCompat.From(context);
                    var channel = new NotificationChannel(channelId, channelName, NotificationImportance.High)
                    {
                        Description = "应用更新通知"
                    };
                    notificationManagerCompat.CreateNotificationChannel(channel);
                }

                // 创建点击通知的Intent
                var intent = new Intent(context, typeof(MainActivity));
                intent.SetFlags(ActivityFlags.SingleTop | ActivityFlags.ClearTop);
                intent.PutExtra("show_update", true);

                var pendingIntent = PendingIntent.GetActivity(context, 0, intent,
                    PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);

                // 构建通知
                var notification = new NotificationCompat.Builder(context, channelId)
                    .SetContentTitle("发现新版本")
                    .SetContentText($"版本 {updateResponse.LatestVersion.VersionNumber} 可用，点击更新")
                    .SetSmallIcon(global::Android.Resource.Drawable.StatSysDownload)
                    .SetAutoCancel(true)
                    .SetContentIntent(pendingIntent)
                    .SetPriority(NotificationCompat.PriorityHigh)
                    .Build();

                // 显示通知
                var notificationManager2 = NotificationManagerCompat.From(context);
                if (notificationManager2.AreNotificationsEnabled())
                {
                    notificationManager2.Notify(1001, notification);
                    _logger.LogInformation("已显示更新通知: 版本 {Version}", updateResponse.LatestVersion.VersionNumber);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示更新通知时发生异常");
            }
        }

        public void Dispose()
        {
            StopAutoUpdateCheck();
            _httpClient?.Dispose();
        }
    }
}
